{"name": "human-core", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@mdi/font": "^7.4.47", "axios": "^1.10.0", "axios-date-reviver": "^1.0.0-beta.2", "pinia": "^3.0.1", "quasar": "^2.18.1", "typescript-functional-extensions": "^2.0.0", "vue": "^3.5.13", "vue-i18n": "^11.1.7", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "rollup-plugin-copy": "^3.5.0", "sass-embedded": "^1.89.2", "typescript": "~5.8.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}