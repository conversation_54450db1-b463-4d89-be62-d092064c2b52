/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    QBtn: typeof import('quasar')['QBtn']
    QDrawer: typeof import('quasar')['QDrawer']
    QIcon: typeof import('quasar')['QIcon']
    QItem: typeof import('quasar')['QItem']
    QItemSection: typeof import('quasar')['QItemSection']
    QList: typeof import('quasar')['QList']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TAccountWidgets: typeof import('@/Shared/components/TAccountWidgets.vue')['default']
  }
}
