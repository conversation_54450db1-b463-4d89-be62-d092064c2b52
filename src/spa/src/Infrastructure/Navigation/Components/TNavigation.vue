<template>
  <q-list class="drawer" v-bind="props">
    <q-item
        v-for="(path, index) in paths"
        :key="index"
        clickable
        v-ripple
        :active="isActive(path)"
        active-class="active"
        :to="path.to"
        v-roles.if="path.to.meta.roles"
    >
      <q-item-section avatar class="avatar">
        <q-icon :name="path.icon"/>
      </q-item-section>

      <q-item-section class="label">
        {{ path.label() }}
      </q-item-section>
    </q-item>
  </q-list>
</template>

<script setup lang="ts">
import {useRoute} from "vue-router";
import type {QListProps} from "quasar";
import NavigationPaths, {type TNavigationPath} from "@/Infrastructure/Navigation/Navigation.ts";
import {computed, ref} from "vue";

interface TDrawerListProps extends QListProps {
}

const props = defineProps<TDrawerListProps>();
const r = useRoute();

const leftDrawerOpen = ref(true);
const paths = computed(() => NavigationPaths);

function isActive(path: TNavigationPath): boolean {
  return r.name === path.to.name;
}
</script>

<style scoped lang="scss">
.drawer {
  padding-top: 20px;

  .q-item {
    border-radius: 0 32px 32px 0;
    padding: 10px 16px 10px 24px;

    .avatar {
      padding-right: 16px;
      min-width: auto;
    }

    .label {
      font-family: 'Google Sans', Roboto, Arial, sans-serif;
      font-size: 0.875rem;
      font-weight: 500;
      letter-spacing: 0.00714286em;
      line-height: 1.25rem;
      hyphens: auto;
      word-break: break-word;
      word-wrap: break-word;
    }
  }

  .active {
    background: #e8eff6;
  }
}
</style>
