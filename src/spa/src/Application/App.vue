<template>
  <div class="layout">
    <t-top-bar class="topbar"/>
    <t-side-bar class="sidebar-left"/>
    <main class="main-content">
      <RouterView/>
    </main>
  </div>
</template>

<script setup lang="ts">
import {RouterView} from "vue-router";
import TTopBar from "@/Shared/Components/TTopBar.vue";
import TSideBar from "@/Shared/Components/TSideBar.vue";
</script>

<style scoped lang="scss">
.layout {
  display: grid;
  grid-template-areas:
    "topbar topbar"
    "sidebar-left main-content";
  grid-template-columns: 300px 1fr;
  grid-template-rows: 50px 1fr;
  height: 100%;
}

.topbar {
  grid-area: topbar;
}

.sidebar-left {
  grid-area: sidebar-left;
}

.main-content {
  grid-area: main-content;
  padding: 1rem;
  overflow-y: auto;
}
</style>
