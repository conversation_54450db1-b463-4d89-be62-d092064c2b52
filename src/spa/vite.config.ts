import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import { setupComponentsPlugin } from './src/infrastructure/configuration/plugins/components'
import copy from "rollup-plugin-copy";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => ['teltonika-account-widgets'].includes(tag)
        }
      }
    }),
    vueDevTools(),
    setupComponentsPlugin(),
    copy({
      targets: [
        {src: "appsettings.json", dest: "dist"}
      ],
      hook: "writeBundle"
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
})
